﻿/*********************************************************************************
// This code is created by SimCoder Version 2025a15.147 for F28004x Hardware Target
//
// SimCoder is copyright by Powersim Inc., 2009-2021
//
// Date: July 29, 2025 16:04:33
**********************************************************************************/
#include	<math.h>
#include	"PS_bios.h"
#define	GetCurTime() PS_GetSysTimer()
#define	iif(a, b, c) ((a) ? (b) : (c))
typedef interrupt void (*ClaIntr)(void);
ClaIntr Cla1Task1 = 0;
ClaIntr Cla1Task2 = 0;
ClaIntr Cla1Task3 = 0;
ClaIntr Cla1Task4 = 0;
ClaIntr Cla1Task5 = 0;
ClaIntr Cla1Task6 = 0;
ClaIntr Cla1Task7 = 0;
ClaIntr Cla1Task8 = 0;




interrupt void Task();

#ifdef _FLASH
#pragma DATA_SECTION(PSK_SysClk, "copysections")
#endif
const Uint16 PSK_SysClk = 100;  // MHz
extern	DefaultType	fGblV16;
extern	DefaultType	fGblVma;
extern	DefaultType	fGblV6;
extern	DefaultType	fGblV15;
extern	DefaultType	fGblV17;
extern	DefaultType	fGblV19;
extern	DefaultType	fGblV20;
extern	DefaultType	fGblUDELAY1;
extern	DefaultType	fGblVsin;
extern	DefaultType	fGblVcos;
extern	DefaultType	fGblV26;

#define	PSM_VRefHiA		3.3		// ADC-A VREFHIA
#define	PSM_VRefHiB		3.3		// ADC-B VREFHIB
#define	PSM_VRefHiC		3.3		// ADC-C VREFHIC






DefaultType	fGblV16 = 0;
DefaultType	fGblVma = 0;
DefaultType	fGblV6 = 0;
DefaultType	fGblV15 = 0;
DefaultType	fGblV17 = 0;
DefaultType	fGblV19 = 0;
DefaultType	fGblV20 = 0;
DefaultType	fGblUDELAY1 = 0;
int32	nGblC_BUFFER_11 = 0;
DefaultType	aryC_BUFFER_11[] = {0,0,0,0,0,0,0,0,0,0};
DefaultType	fGblVsin = 0;
DefaultType	fGblVcos = 0;
DefaultType	fGblV26 = 0;
interrupt void Task()
{
	DefaultType	fC_BUFFER_11, fUDELAY1, fSUMP6, fMULT8, fMULT7, fZOH1, fCOS_R1;
	DefaultType	fP1, fSUM3, fSUMP1, fP2, fFCNM1, fSUM2, fMULT11, fVgain2, fZOH5;
	DefaultType	fPSM_F28004x_ADC2_2, fMULT2, fSIN_R1, fLIMIT_RANGE1, fB1, fSUMP5;
	DefaultType	fVDC3, fLIM3, fS10, fLIM1, fS1, fSUM1, fMULT5, fVgain, fZOH3;
	DefaultType	fPSM_F28004x_ADC2, fVDC1, fMULT6, fVgain1, fZOH4, fPSM_F28004x_ADC2_1;

	ADC_CLR(2) = 1 << (1-1);	// Clear ADC interrupt flag 1
	CPU_PIEACK |= M__INT1;
	fUDELAY1 = fGblUDELAY1;

	fC_BUFFER_11 = aryC_BUFFER_11[nGblC_BUFFER_11];


	fPSM_F28004x_ADC2_1 = ADC_RESULT(2, 1) * (1.0 * PSM_VRefHiC / 4096.0);
	fZOH4 = fPSM_F28004x_ADC2_1;
	fVgain1 = 33.571;
	fMULT6 = fZOH4 * fVgain1;
	fVDC1 = 40;
	fPSM_F28004x_ADC2 = ADC_RESULT(2, 0) * (1.0 * PSM_VRefHiC / 4096.0);
	fZOH3 = fPSM_F28004x_ADC2;
	fVgain = 33.571;
	fMULT5 = fZOH3 * fVgain;
	fSUM1 = fVDC1 - fMULT5;
	{	// backward Euler
		static DefaultType out_A = 0.0;
		fS1 = out_A + (0.008/(0.06*20000)) * fSUM1;
		fS1 = (fS1 < (-10)) ? (-10) : ((fS1 > 10.0) ? 10.0 : fS1);
		out_A = fS1;
		fS1 += 0.008 * fSUM1;
		fS1 = (fS1 < (-10)) ? (-10) : ((fS1 > 10.0) ? 10.0 : fS1);
	}
#ifdef	_DEBUG
	fGblV16 = fS1;
#endif

	fLIM1 = (fS1 > 15) ? 15 : ((fS1 < 0) ? 0 : fS1);
	{	// backward Euler
		static DefaultType out_A = 0.0;
		fS10 = out_A + (4.85/(0.04*20000)) * fUDELAY1;
		fS10 = (fS10 < (-10)) ? (-10) : ((fS10 > 10.0) ? 10.0 : fS10);
		out_A = fS10;
		fS10 += 4.85 * fUDELAY1;
		fS10 = (fS10 < (-10)) ? (-10) : ((fS10 > 10.0) ? 10.0 : fS10);
	}
	fLIM3 = (fS10 > 1) ? 1 : ((fS10 < (-1)) ? (-1) : fS10);
	fVDC3 = 313.3;
	fSUMP5 = fLIM3 + fVDC3;
	{
		static DefaultType out_A = 0;
		static DefaultType in_A = 0.0;
		fB1 = out_A + 0.5/20000 * (fSUMP5 + in_A);
		out_A = fB1;in_A = fSUMP5;
	}
	fLIMIT_RANGE1 = fB1;
	for (;;)
	{
		if (fLIMIT_RANGE1 >= (2.0*3.1416))
			fLIMIT_RANGE1 -= (2.0*3.1416) - ((-2)*3.1416);
		else if (fLIMIT_RANGE1 < ((-2)*3.1416))
			fLIMIT_RANGE1 += (2.0*3.1416) - ((-2)*3.1416);
		else
			break;
	}
	fSIN_R1 = sin(fLIMIT_RANGE1);
	fMULT2 = fLIM1 * fSIN_R1;
	fPSM_F28004x_ADC2_2 = ADC_RESULT(2, 2) * (1.0 * PSM_VRefHiC / 4096.0);
	fZOH5 = fPSM_F28004x_ADC2_2;
	fVgain2 = 1.0/0.264;
	fMULT11 = fZOH5 * fVgain2;
	fSUM2 = fMULT2 - fMULT11;
	fFCNM1 = 50.0/((1.0*1.0)+(314.0*314.0));
	fP2 = fSUM2 * 100;
	fSUMP1 = fFCNM1 + fP2;
	fSUM3 = fMULT6 - fSUMP1;
	fP1 = fSUM3 * (28.0/940.0);
#ifdef	_DEBUG
	fGblVma = fP1;
#endif
#ifdef	_DEBUG
	fGblV6 = fLIM1;
#endif
#ifdef	_DEBUG
	fGblV15 = fMULT2;
#endif
#ifdef	_DEBUG
	fGblV17 = fSUMP1;
#endif
#ifdef	_DEBUG
	fGblV19 = fSUM1;
#endif
#ifdef	_DEBUG
	fGblV20 = fSUM2;
#endif
	fCOS_R1 = cos(fLIMIT_RANGE1);
	fZOH1 = fMULT6;
	fMULT7 = fCOS_R1 * fZOH1;
	fMULT8 = fSIN_R1 * fC_BUFFER_11;
	fSUMP6 = fMULT7 + fMULT8;
	fGblUDELAY1 = fSUMP6;
	aryC_BUFFER_11[nGblC_BUFFER_11] = fZOH1; nGblC_BUFFER_11 = (nGblC_BUFFER_11 >= 10 - 1) ? 0 : (nGblC_BUFFER_11 + 1);
#ifdef	_DEBUG
	fGblVsin = fSIN_R1;
#endif
#ifdef	_DEBUG
	fGblVcos = fCOS_R1;
#endif
#ifdef	_DEBUG
	fGblV26 = fSUM3;
#endif
	// Start of changing PWM1(1ph) registers
	// Set Duty Cycle
	{
		DefaultType _val = __fsat(fP1, 2 + 0, 0);
		_val = ((Uint32)(PWM_TBPRD(1))+1) * ((_val - 0) * (1.0 / 2));
		PWM_CMPA(1) = (int)_val;
	}
	// End of changing PWM1(1ph) registers
}


void Initialize(void)
{
	PS_SysInit(2, 20);
	PS_PwmStartStopClock(0);	// Stop Pwm Clock
	PS_TimerInit(0, 0);
	PS_SetVREF(0, 1, 0);	// Set external VRef for ADC-A
	PS_SetVREF(1, 1, 0);	// Set external VRef for ADC-B
	PS_SetVREF(2, 1, 0);	// Set external VRef for ADC-C

	{
	    int i, preAdcNo = -1;
	    /* PST_AdcAttr: Adc No., Channel No., Soc No., Trig Src, SampleTime(clock) */
	    const PST_AdcAttr aryAdcInit[3] = {
			{2, 0, 0, ADCTRIG_PWM1, 32},
			{2, 1, 1, ADCTRIG_PWM1, 32},
			{2, 2, 2, ADCTRIG_PWM1, 32}};
	    const PST_AdcAttr *p = aryAdcInit;
	    for (i = 0; i < 3; i++, p++) {
	        if (preAdcNo != p->nAdcNo) {
	            PS_AdcInit(p->nAdcNo);
	            preAdcNo = p->nAdcNo;
	        }
	        PS_AdcSetChn(p->nAdcNo, p->nChnNo, p->nSocNo, p->nTrigSrc, p->nWindSz);
	    }
	}

	PS_PwmInit(1, 0, 0, 1.e6/(20000*1.0), ePwmUseAB, ePwmStartHigh1, ePwmComplement, HRPWM_DISABLE);	// pwmNo, pinSel, waveType, period, outtype, PwmA, PWMB, UseHRPwm
	PS_PwmSetDeadBand(1, 0, 2, 3, 0, 2, 2);
	PS_PwmSetIntrType(1, ePwmIntrAdc, 1, 0);
	PS_AdcSetIntr(2, 1, 2, Task); // AdcNo, IntrNo, SocNo, Interrupt Vector
	PS_PwmSetTripAction(1, eTzHiZ, eTzHiZ);
	PWM_CMPA(1) = (0 - 0) / (1.0 * 2) * PWM_TBPRD(1);
	PSM_PwmStart(1);

	PS_PwmStartStopClock(1);	// Start Pwm Clock
}


void main()
{
	Initialize();
	PSM_EnableIntr();   // Enable Global interrupt INTM
	PSM_EnableDbgm();
	for (;;) {
	}
}

